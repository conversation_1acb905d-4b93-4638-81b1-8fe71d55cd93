import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Otimizações para melhorar a performance de navegação
  reactStrictMode: false, // Desativado para evitar problemas de hidratação
  poweredByHeader: false,
  compiler: {
    removeConsole: process.env.NODE_ENV === "production" ? {
      exclude: ["error", "warn"],
    } : false,
  },
  // Ignorar erros de ESLint durante o build
  eslint: {
    ignoreDuringBuilds: true,
  },
  experimental: {
    optimizeCss: true,
    optimisticClientCache: true,
  },
  // Desativar recursos de SEO
  headers: async () => {
    return [
      {
        source: '/:path*',
        headers: [
          {
            key: 'X-Robots-Tag',
            value: 'noindex, nofollow, noarchive, nosnippet, noimageindex',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'no-referrer',
          },
        ],
      },
    ];
  },
  // Configuração para o Firebase Hosting
  output: 'standalone',
  // Desativar a compilação de imagens para evitar problemas
  images: {
    unoptimized: true,
  },
};

export default nextConfig;
