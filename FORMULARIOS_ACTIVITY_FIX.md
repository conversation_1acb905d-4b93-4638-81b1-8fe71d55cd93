# Fix: Formulários não aparecem no feed de atividades para utilizadores comuns

## Problema Identificado

Quando os utilizadores carregam ficheiros através da página de Formulários, estes não aparecem no feed de atividades para utilizadores comuns (não administradores). <PERSON><PERSON><PERSON> disso, ocorrem erros de índices do Firebase.

### Erros Específicos:
1. **Firebase Index Error**: `The query requires an index. That index is currently building and cannot be used yet.`
2. **Atividades não aparecem**: Formulários carregados não são visíveis no feed de atividades para membros de equipa

## Causa Raiz

### 1. Campo `teamId` em falta
- O componente `FileUpload` não estava a incluir o `teamId` quando salvava formulários no Firestore
- A interface `Formulario` não incluía o campo `teamId`
- O serviço de atividades filtra por `teamId` para utilizadores não-admin, mas os formulários não tinham este campo

### 2. Índices Firebase em falta
- Faltavam índices compostos para as queries que filtram por `teamId` e ordenam por data
- Especificamente para as coleções `formularios` e `activities`

## Solução Implementada

### 1. Atualização do FileUpload Component
**Ficheiro**: `components/file-upload.tsx`

- Adicionado `userTeamId` do contexto de autenticação
- Incluído `teamId` nos dados do ficheiro enviados para o callback
- Atualizada a interface `FileUploadProps` para incluir `teamId`

```typescript
// Antes
const fileData = {
  name: file.name,
  title: title,
  url: downloadURL,
  type: file.type,
  size: file.size,
  uploadedAt: new Date(),
  uploadedBy: user.uid,
};

// Depois
const fileData = {
  name: file.name,
  title: title,
  url: downloadURL,
  type: file.type,
  size: file.size,
  uploadedAt: new Date(),
  uploadedBy: user.uid,
  teamId: userTeamId,
};
```

### 2. Atualização da Interface Formulario
**Ficheiro**: `components/formularios-table.tsx`

- Adicionado campo `teamId?: string` à interface `Formulario`

### 3. Índices Firebase
**Ficheiro**: `firestore.indexes.json`

Adicionados os seguintes índices compostos:

```json
{
  "collectionGroup": "formularios",
  "queryScope": "COLLECTION",
  "fields": [
    { "fieldPath": "teamId", "order": "ASCENDING" },
    { "fieldPath": "uploadedAt", "order": "DESCENDING" }
  ]
},
{
  "collectionGroup": "activities",
  "queryScope": "COLLECTION",
  "fields": [
    { "fieldPath": "teamId", "order": "ASCENDING" },
    { "fieldPath": "timestamp", "order": "DESCENDING" }
  ]
}
```

### 4. Script de Deploy
**Ficheiro**: `deploy-indexes.sh`

Criado script para facilitar o deploy dos índices:
```bash
./deploy-indexes.sh
```

## Como Aplicar a Correção

### 1. Deploy dos Índices (OBRIGATÓRIO)
```bash
# Opção 1: Usar o script
./deploy-indexes.sh

# Opção 2: Comando direto
firebase deploy --only firestore:indexes
```

### 2. Aguardar Construção dos Índices
- Os índices podem demorar alguns minutos a construir
- Verificar o progresso em: https://console.firebase.google.com/v1/r/project/woomanagerapp-77606/firestore/indexes

### 3. Testar a Funcionalidade
1. Fazer login como utilizador comum (não admin)
2. Carregar um ficheiro na página de Formulários
3. Verificar se a atividade aparece no feed de atividades

## Comportamento Esperado Após a Correção

### Para Administradores:
- Veem todas as atividades de formulários de todas as equipas
- Comportamento inalterado

### Para Membros de Equipa:
- Veem apenas atividades de formulários da sua equipa
- Formulários carregados por membros da mesma equipa aparecem no feed
- Formulários carregados por outras equipas não aparecem

### Para Utilizadores sem Equipa:
- Não veem atividades de formulários (comportamento correto)

## Verificação da Correção

1. **Índices construídos**: Verificar no Firebase Console que os índices estão "Enabled"
2. **Sem erros de índice**: Não devem aparecer mais erros sobre índices em falta
3. **Atividades visíveis**: Formulários carregados aparecem no feed para utilizadores da mesma equipa
4. **Filtragem correta**: Utilizadores só veem atividades da sua equipa

## Notas Técnicas

- O campo `teamId` é opcional para manter compatibilidade com formulários existentes
- Formulários sem `teamId` só serão visíveis para administradores
- A lógica de filtragem por equipa já existia no serviço de atividades
- Esta correção não afeta outros tipos de atividades (registos, condutores, etc.)
