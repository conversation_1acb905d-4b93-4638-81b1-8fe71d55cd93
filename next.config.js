/** @type {import('next').NextConfig} */

// Carregar variáveis de ambiente do arquivo .env.local
// Isso é feito automaticamente pelo Next.js, mas podemos forçar o carregamento
// para garantir que as variáveis estejam disponíveis
const dotenv = require('dotenv');
dotenv.config({ path: '.env.local' });

const nextConfig = {
  reactStrictMode: true,
  // Desabilitar a verificação de ESLint durante o build
  eslint: {
    // Ignorar os erros de ESLint durante o build
    ignoreDuringBuilds: true,
  },
  // Melhorar o tratamento de componentes do lado do cliente
  serverExternalPackages: ['next-themes', 'sonner'],
  // PWA configuração removida temporariamente para evitar avisos
  // Configuração para o Firebase
  webpack: (config, { isServer }) => {
    // Configuração para evitar que o Firebase seja incluído no bundle do servidor
    if (isServer) {
      config.externals = [...config.externals, 'firebase', 'firebase/app', 'firebase/auth', 'firebase/firestore', 'firebase/storage'];
    }
    return config;
  },
  // Publicar variáveis de ambiente para o cliente
  // Isso é necessário para que as variáveis de ambiente estejam disponíveis no cliente
  env: {
    NEXT_PUBLIC_FIREBASE_API_KEY: "AIzaSyAsZCGeVLNE7Tu1_HdiAnX80R0ZxKXX_1U",
    NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: "woomanagerapp-77606.firebaseapp.com",
    NEXT_PUBLIC_FIREBASE_PROJECT_ID: "woomanagerapp-77606",
    NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: "woomanagerapp-77606.firebasestorage.app",
    NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: "1087381766306",
    NEXT_PUBLIC_FIREBASE_APP_ID: "1:1087381766306:web:9f616099c9a19bae6017fb",
    NEXT_PUBLIC_FIREBASE_DATABASE_URL: "https://woomanagerapp-77606-default-rtdb.europe-west1.firebasedatabase.app",
  },
};

module.exports = nextConfig;
