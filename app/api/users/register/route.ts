import { NextRequest, NextResponse } from 'next/server';
import admin, { adminAuth, adminFirestore } from '@/lib/firebase-admin';
import { UserRole } from '@/types/team';

export async function POST(request: NextRequest) {
  try {
    console.log("API de registo de utilizador chamada");
    // Verificar se o utilizador está autenticado e é admin
    // Nota: Em produção, deve-se implementar uma verificação de autenticação adequada
    // Esta é uma implementação simplificada

    // Verificar se o Firebase Admin SDK está inicializado corretamente
    if (!adminAuth || !adminFirestore) {
      console.error("Firebase Admin SDK não está inicializado corretamente");
      return NextResponse.json(
        { error: 'Erro interno do servidor: Firebase Admin SDK não está inicializado' },
        { status: 500 }
      );
    }

    const body = await request.json();
    console.log("Dados recebidos:", body);
    const { email, password, fullName, registrationNumber, category, role, teamId, canEditProfile } = body;

    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email e password são obrigatórios' },
        { status: 400 }
      );
    }

    console.log("Verificando credenciais do Firebase Admin...");
    console.log("adminAuth disponível:", !!adminAuth);
    console.log("adminFirestore disponível:", !!adminFirestore);

    // Criar o utilizador no Firebase Authentication
    console.log("Criando utilizador no Firebase Authentication...");
    const userRecord = await adminAuth.createUser({
      email,
      password,
      displayName: fullName,
      emailVerified: false,
    });
    console.log("Utilizador criado com sucesso:", userRecord.uid);

    // Preparar os dados do perfil do utilizador
    const userData = {
      fullName,
      registrationNumber,
      category,
      email,
      role: role || null,
      teamId: teamId || null,
      canEditProfile: canEditProfile !== false,
      profileCompleted: true, // O admin já preencheu o perfil
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Guardar os dados do utilizador no Firestore
    console.log("Guardando dados do utilizador no Firestore...");
    await adminFirestore.collection('users').doc(userRecord.uid).set(userData);
    console.log("Dados do utilizador guardados com sucesso");

    // Se o utilizador for atribuído a uma equipa, atualizar a equipa
    if (teamId && (role === UserRole.TEAM_LEADER || role === UserRole.TEAM_MEMBER)) {
      console.log(`Atualizando equipa ${teamId} para incluir o utilizador ${userRecord.uid}`);
      const teamRef = adminFirestore.collection('teams').doc(teamId);
      const teamDoc = await teamRef.get();

      if (teamDoc.exists) {
        // Atualizar o array apropriado na equipa
        if (role === UserRole.TEAM_LEADER) {
          console.log(`Adicionando utilizador ${userRecord.uid} como líder da equipa ${teamId}`);
          await teamRef.update({
            leaders: admin.firestore.FieldValue.arrayUnion(userRecord.uid),
            updatedAt: new Date()
          });
        } else if (role === UserRole.TEAM_MEMBER) {
          console.log(`Adicionando utilizador ${userRecord.uid} como membro da equipa ${teamId}`);
          await teamRef.update({
            members: admin.firestore.FieldValue.arrayUnion(userRecord.uid),
            updatedAt: new Date()
          });
        }
        console.log(`Equipa ${teamId} atualizada com sucesso`);
      } else {
        console.warn(`Equipa ${teamId} não encontrada`);
      }
    }

    // Enviar email de convite (link para redefinir a senha)
    const actionCodeSettings = {
      url: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/login`,
      handleCodeInApp: false,
    };

    console.log("Gerando link de redefinição de password...");
    const resetLink = await adminAuth.generatePasswordResetLink(email, actionCodeSettings);
    console.log("Link de redefinição de password gerado com sucesso");

    // Em produção, deve-se enviar o email usando um serviço de email
    // Aqui, apenas retornamos o link para fins de demonstração

    return NextResponse.json({
      success: true,
      message: 'Utilizador criado com sucesso',
      userId: userRecord.uid,
      resetLink,
    });
  } catch (error: any) {
    console.error('Erro ao registar utilizador:', error);
    console.error('Stack trace:', error.stack);

    // Tratar erros específicos do Firebase
    if (error.code === 'auth/email-already-exists') {
      return NextResponse.json(
        { error: 'O email já está em uso por outro utilizador' },
        { status: 400 }
      );
    }

    if (error.code === 'auth/invalid-email') {
      return NextResponse.json(
        { error: 'O email fornecido é inválido' },
        { status: 400 }
      );
    }

    if (error.code === 'auth/weak-password') {
      return NextResponse.json(
        { error: 'A password é muito fraca' },
        { status: 400 }
      );
    }

    // Verificar se é um erro de permissão
    if (error.code === 'auth/insufficient-permission' || error.message?.includes('Permission denied')) {
      return NextResponse.json(
        { error: 'Permissão negada. Verifique se as credenciais do Firebase Admin têm permissões suficientes.' },
        { status: 403 }
      );
    }

    return NextResponse.json(
      { error: 'Erro ao registar utilizador', details: error.message, code: error.code },
      { status: 500 }
    );
  }
}
