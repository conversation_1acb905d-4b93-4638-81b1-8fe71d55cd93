import { NextRequest, NextResponse } from 'next/server';
import { adminAuth, adminDb } from '@/lib/firebase-admin';
import { logDeletionActivity, ActivityType } from '@/services/activity-service';

export async function POST(request: NextRequest) {
  try {
    // Verificar se o utilizador está autenticado
    const authHeader = request.headers.get('authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Token de autorização necessário' }, { status: 401 });
    }

    const token = authHeader.split('Bearer ')[1];
    let decodedToken;

    try {
      decodedToken = await adminAuth.verifyIdToken(token);
    } catch (error) {
      console.error('Erro ao verificar token:', error);
      return NextResponse.json({ error: 'Token inválido' }, { status: 401 });
    }

    // Verificar se o utilizador é administrador
    const userDoc = await adminDb.collection('users').doc(decodedToken.uid).get();
    if (!userDoc.exists || userDoc.data()?.role !== 'admin') {
      return NextResponse.json({ error: 'Acesso negado. Apenas administradores podem eliminar utilizadores.' }, { status: 403 });
    }

    const { userIds } = await request.json();

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json({ error: 'Lista de IDs de utilizadores é obrigatória' }, { status: 400 });
    }

    // Verificar se o utilizador não está tentando eliminar a si mesmo
    if (userIds.includes(decodedToken.uid)) {
      return NextResponse.json({ error: 'Não é possível eliminar o seu próprio utilizador' }, { status: 400 });
    }

    const results = [];
    const errors = [];

    for (const userId of userIds) {
      try {
        // Obter dados do utilizador antes de eliminar para logging
        const userToDelete = await adminDb.collection('users').doc(userId).get();
        const userData = userToDelete.data();

        if (!userToDelete.exists) {
          errors.push({ userId, error: 'Utilizador não encontrado' });
          continue;
        }

        // Eliminar o utilizador do Firebase Auth
        await adminAuth.deleteUser(userId);

        // Eliminar o documento do utilizador do Firestore
        await adminDb.collection('users').doc(userId).delete();

        // Remover o utilizador das equipas (se aplicável)
        if (userData?.teamId) {
          const teamRef = adminDb.collection('teams').doc(userData.teamId);
          const teamDoc = await teamRef.get();

          if (teamDoc.exists) {
            const teamData = teamDoc.data();
            const updateData: any = {};

            // Remover das listas de líderes e membros
            if (teamData?.leaders && teamData.leaders.includes(userId)) {
              updateData.leaders = teamData.leaders.filter((id: string) => id !== userId);
            }
            if (teamData?.members && teamData.members.includes(userId)) {
              updateData.members = teamData.members.filter((id: string) => id !== userId);
            }

            if (Object.keys(updateData).length > 0) {
              updateData.updatedAt = new Date();
              await teamRef.update(updateData);
            }
          }
        }

        // Registar atividade de eliminação
        await logDeletionActivity(
          ActivityType.USER_DELETED,
          "Utilizador Eliminado",
          `Utilizador ${userData?.fullName || 'Desconhecido'} foi eliminado do sistema`,
          decodedToken.uid,
          userData?.teamId,
          {
            deletedUserId: userId,
            deletedUserName: userData?.fullName,
            deletedUserEmail: userData?.email,
            deletedUserRole: userData?.role
          }
        );

        results.push({ userId, success: true });
      } catch (error) {
        console.error(`Erro ao eliminar utilizador ${userId}:`, error);
        errors.push({ 
          userId, 
          error: error instanceof Error ? error.message : 'Erro desconhecido' 
        });
      }
    }

    // Registar atividade de bulk delete
    if (results.length > 0) {
      await logDeletionActivity(
        ActivityType.USER_DELETED,
        "Eliminação em Bulk",
        `${results.length} utilizador${results.length > 1 ? 'es foram eliminados' : ' foi eliminado'} do sistema`,
        decodedToken.uid,
        undefined,
        {
          deletedCount: results.length,
          totalRequested: userIds.length,
          errorCount: errors.length,
          deletedUsers: results.map(r => r.userId)
        }
      );
    }

    return NextResponse.json({
      message: `${results.length} utilizador${results.length > 1 ? 'es eliminados' : ' eliminado'} com sucesso`,
      results,
      errors: errors.length > 0 ? errors : undefined
    });

  } catch (error) {
    console.error('Erro ao eliminar utilizadores em bulk:', error);
    return NextResponse.json(
      { error: 'Erro interno do servidor ao eliminar utilizadores' },
      { status: 500 }
    );
  }
}
