import { NextResponse } from 'next/server';
import { adminAuth, adminFirestore } from '@/lib/firebase-admin';

export async function GET() {
  try {
    console.log("API de teste do Firebase Admin chamada");

    // Verificar se o Firebase Admin SDK está inicializado corretamente
    console.log("adminAuth disponível:", !!adminAuth);
    console.log("adminFirestore disponível:", !!adminFirestore);

    // Tentar listar os usuários (limitado a 1 para não sobrecarregar)
    try {
      const listUsersResult = await adminAuth.listUsers(1);
      console.log("Listagem de usuários bem-sucedida:", listUsersResult.users.length, "usuários encontrados");
    } catch (error: unknown) {
      console.error("Erro ao listar usuários:", error);
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      return NextResponse.json(
        { error: 'Erro ao listar usuários', details: errorMessage },
        { status: 500 }
      );
    }

    // Tentar acessar o Firestore
    try {
      const usersCollection = await adminFirestore.collection('users').limit(1).get();
      console.log("Acesso ao Firestore bem-sucedido:", usersCollection.size, "documentos encontrados");
    } catch (error: unknown) {
      console.error("Erro ao acessar o Firestore:", error);
      const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
      return NextResponse.json(
        { error: 'Erro ao acessar o Firestore', details: errorMessage },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Firebase Admin SDK está funcionando corretamente',
    });
  } catch (error: unknown) {
    console.error('Erro no teste do Firebase Admin:', error);
    const errorMessage = error instanceof Error ? error.message : 'Erro desconhecido';
    return NextResponse.json(
      { error: 'Erro no teste do Firebase Admin', details: errorMessage },
      { status: 500 }
    );
  }
}
