"use client";

import { useState } from "react";
import { LocationAutocomplete } from "@/components/location-autocomplete";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

export default function LocationAutocompleteExample() {
  const [location, setLocation] = useState("");
  const [useNormalizedAddresses, setUseNormalizedAddresses] = useState(true);
  const [removeDuplicates, setRemoveDuplicates] = useState(true);

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-2xl font-bold mb-6">Exemplo de Pesquisa de Localização</h1>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Configurações</CardTitle>
            <CardDescription>
              Ajuste as configurações para ver como o componente se comporta
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <Label htmlFor="normalize-addresses">
                Usar endereços normalizados (sem números de porta/códigos postais)
              </Label>
              <Switch 
                id="normalize-addresses" 
                checked={useNormalizedAddresses} 
                onCheckedChange={setUseNormalizedAddresses} 
              />
            </div>
            
            <div className="flex items-center justify-between">
              <Label htmlFor="remove-duplicates">
                Remover endereços duplicados
              </Label>
              <Switch 
                id="remove-duplicates" 
                checked={removeDuplicates} 
                onCheckedChange={setRemoveDuplicates} 
              />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Pesquisa de Localização</CardTitle>
            <CardDescription>
              Digite um endereço para ver os resultados
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <LocationAutocomplete
              value={location}
              onChange={setLocation}
              placeholder="Digite um endereço..."
              useNormalizedAddresses={useNormalizedAddresses}
              removeDuplicates={removeDuplicates}
            />
            
            {location && (
              <div className="mt-4 p-4 border rounded-md">
                <p className="text-sm font-medium">Localização selecionada:</p>
                <p className="text-sm mt-1">{location}</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      
      <div className="mt-8">
        <Card>
          <CardHeader>
            <CardTitle>Como funciona</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm">
              Este componente usa a API OpenCage Data para buscar localizações. Quando a opção "Usar endereços normalizados" 
              está ativada, os endereços são processados para remover números de porta e códigos postais, mostrando apenas 
              o nome da rua, cidade, estado e país.
            </p>
            <p className="text-sm mt-2">
              A opção "Remover endereços duplicados" elimina resultados que têm o mesmo endereço normalizado, 
              evitando mostrar o mesmo local várias vezes com pequenas variações.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
