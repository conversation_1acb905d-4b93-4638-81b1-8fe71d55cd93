import { initializeApp, getApps, getApp, FirebaseApp } from "firebase/app";
import { getAuth, setPersistence, browserLocalPersistence, Auth } from "firebase/auth";
import { getFirestore, Firestore } from "firebase/firestore";
import { getStorage, FirebaseStorage } from "firebase/storage";

// Configuração do Firebase usando valores diretos
// Isso garante que a configuração funcione mesmo se as variáveis de ambiente não estiverem disponíveis
const firebaseConfig = {
  apiKey: "AIzaSyAsZCGeVLNE7Tu1_HdiAnX80R0ZxKXX_1U",
  authDomain: "woomanagerapp-77606.firebaseapp.com",
  projectId: "woomanagerapp-77606",
  storageBucket: "woomanagerapp-77606.firebasestorage.app",
  messagingSenderId: "1087381766306",
  appId: "1:1087381766306:web:9f616099c9a19bae6017fb",
  databaseURL: "https://woomanagerapp-77606-default-rtdb.europe-west1.firebasedatabase.app",
};

// Verificar se a configuração está completa
const isConfigValid =
  firebaseConfig.apiKey &&
  firebaseConfig.authDomain &&
  firebaseConfig.projectId &&
  firebaseConfig.storageBucket &&
  firebaseConfig.messagingSenderId &&
  firebaseConfig.appId;

if (!isConfigValid) {
  console.error("Configuração do Firebase incompleta:", firebaseConfig);
}

// Inicializar Firebase com valores padrão
let app: FirebaseApp = {} as FirebaseApp;
let auth: Auth = {} as Auth;
let db: Firestore = {} as Firestore;
let storage: FirebaseStorage = {} as FirebaseStorage;

try {
  // Verificar se o Firebase já foi inicializado
  if (!getApps().length) {
    console.log("Inicializando Firebase pela primeira vez");
    app = initializeApp(firebaseConfig);
  } else {
    console.log("Firebase já inicializado, obtendo instância existente");
    app = getApp();
  }

  // Inicializar serviços
  auth = getAuth(app);
  db = getFirestore(app);
  storage = getStorage(app);

  // Configurar persistência no lado do cliente
  if (typeof window !== 'undefined') {
    setPersistence(auth, browserLocalPersistence)
      .then(() => {
        console.log("Persistência do Firebase configurada com sucesso");
      })
      .catch((error) => {
        console.error("Erro ao configurar persistência do Firebase:", error);
      });
  }

  console.log("Firebase inicializado com sucesso");
} catch (error) {
  console.error("Erro crítico ao inicializar Firebase:", error);

  // Em caso de erro, criar objetos vazios para evitar quebras na aplicação
  if (!app) app = {} as unknown as FirebaseApp;
  if (!auth) auth = {} as unknown as Auth;
  if (!db) db = {} as unknown as Firestore;
  if (!storage) storage = {} as unknown as FirebaseStorage;
}

// Exportar instâncias
export { app, auth, db, storage };
