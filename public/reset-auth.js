// Script para limpar completamente o cache e forçar nova autenticação
(function() {
  console.log('Iniciando limpeza completa de cache e autenticação...');
  
  // Função para limpar todos os caches e dados de autenticação
  const resetAuth = async () => {
    try {
      // 1. Limpar localStorage
      if (window.localStorage) {
        console.log('Limpando localStorage...');
        window.localStorage.clear();
      }
      
      // 2. Limpar sessionStorage
      if (window.sessionStorage) {
        console.log('Limpando sessionStorage...');
        window.sessionStorage.clear();
      }
      
      // 3. Limpar cookies
      console.log('Limpando cookies...');
      document.cookie.split(';').forEach(function(c) {
        document.cookie = c.trim().split('=')[0] + '=;expires=Thu, 01 Jan 1970 00:00:00 UTC;path=/';
      });
      
      // 4. Limpar caches de aplicação
      if ('caches' in window) {
        console.log('Limpando caches de aplicação...');
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => {
            console.log('Limpando cache:', cacheName);
            return caches.delete(cacheName);
          })
        );
      }
      
      // 5. Desregistrar Service Workers
      if ('serviceWorker' in navigator) {
        console.log('Desregistrando Service Workers...');
        const registrations = await navigator.serviceWorker.getRegistrations();
        for (let registration of registrations) {
          await registration.unregister();
          console.log('Service Worker desregistrado');
        }
      }
      
      // 6. Limpar IndexedDB
      console.log('Limpando IndexedDB...');
      const databases = await window.indexedDB.databases();
      databases.forEach(db => {
        if (db.name) {
          window.indexedDB.deleteDatabase(db.name);
          console.log('IndexedDB apagado:', db.name);
        }
      });
      
      console.log('Limpeza completa finalizada!');
      console.log('Redirecionando para a página de login...');
      
      // Redirecionar para a página de login após um pequeno atraso
      setTimeout(() => {
        window.location.href = '/login';
      }, 1000);
    } catch (error) {
      console.error('Erro durante a limpeza:', error);
      // Mesmo em caso de erro, tentar redirecionar para a página de login
      window.location.href = '/login';
    }
  };
  
  // Executar a limpeza quando a página carregar
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', resetAuth);
  } else {
    resetAuth();
  }
})();
