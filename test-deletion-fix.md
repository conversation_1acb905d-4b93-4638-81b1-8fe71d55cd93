# Deletion Error Fix Summary

## Problem
JavaScript error occurring during deletion operations across multiple modules:
```
TypeError: Cannot read properties of undefined (reading 'indexOf')
    at ResourcePath.fromString (webpack-internal:///(app-pages-browser)/./node_modules/@firebase/firestore/dist/index.esm2017.js:1253:19)
    at doc (webpack-internal:///(app-pages-browser)/./node_modules/@firebase/firestore/dist/index.esm2017.js:18804:32)
    at getUser (webpack-internal:///(app-pages-browser)/./services/users-service.ts:71:145)
```

## Root Cause
The deletion service functions expect two parameters (`id` and `userId`), but client-page components were only passing the `id` parameter. This caused `userId` to be `undefined`, which then caused Firebase's `doc()` function to fail when trying to create a document reference.

## Files Fixed

### 1. app/(protected)/textos/client-page.tsx
- **Issue**: `handleDelete` was calling `deleteTexto(id)` without `userId`
- **Fix**: Added user authentication check and pass `user.uid` as second parameter
- **Change**: `await deleteTexto(id, user.uid)`

### 2. app/(protected)/nips/client-page.tsx
- **Issue**: `handleDelete` was calling `deleteNIP(id)` without `userId`
- **Fix**: Added user authentication check and pass `user.uid` as second parameter
- **Change**: `await deleteNIP(id, user.uid)`

### 3. app/(protected)/nips-pessoas/client-page.tsx
- **Issue**: `handleDelete` was calling `deleteNIPPessoa(id)` without `userId`
- **Fix**: Added user authentication check and pass `user.uid` as second parameter
- **Change**: `await deleteNIPPessoa(id, user.uid)`

### 4. app/(protected)/contactos/client-page.tsx
- **Issue**: `handleDelete` was calling `deleteContacto(id)` without `userId`
- **Fix**: Added user authentication check and pass `user.uid` as second parameter
- **Change**: `await deleteContacto(id, user.uid)`

### 5. app/(protected)/formularios/client-page.tsx
- **Issue**: `handleDelete` was calling `deleteFormulario(id, formulario.url)` without `userId`
- **Fix**: Added user authentication check and pass `user.uid` as third parameter
- **Change**: `await deleteFormulario(id, formulario.url, user.uid)`

### 6. app/(protected)/condutores/client-page.tsx
- **Issue**: `handleDeleteCondutor` was calling `deleteCondutor(id)` without `userId`
- **Fix**: Added user authentication check and pass `user.uid` as second parameter
- **Change**: `await deleteCondutor(id, user.uid)`

### 7. components/identificacoes-table.tsx
- **Issue**: `handleDelete` was calling `deleteIdentificacao(selectedIdentificacao.id)` without `userId`
- **Fix**: Added Firebase auth import and user authentication check
- **Change**: `await deleteIdentificacao(selectedIdentificacao.id, currentUser.uid)`

## Pattern Applied
For each fix, the following pattern was applied:

```typescript
const handleDelete = async (id: string) => {
  if (!user?.uid) {
    console.error("Usuário não autenticado");
    throw new Error("Usuário não autenticado");
  }

  try {
    await deleteServiceFunction(id, user.uid);
    // ... rest of the logic
  } catch (error) {
    console.error("Erro ao excluir:", error);
    throw error;
  }
};
```

## Additional Fix: Activity Service teamName Issue

### Problem
After fixing the userId parameter issue, a new Firebase error appeared:
```
FirebaseError: Function addDoc() called with invalid data. Unsupported field value: undefined (found in field teamName in document activities/...)
```

### Root Cause
The `logDeletionActivity` function was trying to access `userInfo.teamName`, but the `getUser` function only returns `teamId`, not `teamName`.

### Solution
Updated `services/activity-service.ts` to:
1. Get the team name by calling `getTeam(teamId)` when a teamId is available
2. Handle cases where team information might not be available
3. Use dynamic import to avoid circular dependencies

### Code Change
```typescript
// Get team name if teamId is available
const finalTeamId = teamId || userInfo.teamId;
let teamName: string | undefined;

if (finalTeamId) {
  try {
    const { getTeam } = await import("./teams-service");
    const teamInfo = await getTeam(finalTeamId);
    teamName = teamInfo?.name;
  } catch (error) {
    console.error("Erro ao obter nome da equipa:", error);
    teamName = undefined;
  }
}
```

## Additional Fix: Form Upload teamId Issue

### Problem
After fixing the deletion issues, a new Firebase error appeared when admin users tried to upload forms:
```
FirebaseError: Function addDoc() called with invalid data. Unsupported field value: undefined (found in field teamId in document formularios/...)
```

### Root Cause
The `FileUpload` component was setting `teamId: userTeamId` in the form data, but for admin users, `userTeamId` is `undefined`. Firebase doesn't allow `undefined` values in documents.

### Solution
Updated `components/file-upload.tsx` to:
1. Only include the `teamId` field in the document if it has a valid value
2. Use conditional assignment to avoid undefined values

### Code Change
```typescript
// Before
const fileData = {
  name: file.name,
  title: title,
  url: downloadURL,
  type: file.type,
  size: file.size,
  uploadedAt: new Date(),
  uploadedBy: user.uid,
  teamId: userTeamId, // This could be undefined for admins
};

// After
const fileData: any = {
  name: file.name,
  title: title,
  url: downloadURL,
  type: file.type,
  size: file.size,
  uploadedAt: new Date(),
  uploadedBy: user.uid,
};

// Only include teamId if it has a valid value (not undefined)
if (userTeamId) {
  fileData.teamId = userTeamId;
}
```

## Additional Fix: Activity Logging teamId Issue for Admin Users

### Problem
After fixing the form upload issue, a new Firebase error appeared during deletion operations for both admin and team users:
```
FirebaseError: Function addDoc() called with invalid data. Unsupported field value: undefined (found in field teamId in document activities/...)
```

### Root Cause
The `logDeletionActivity` function was trying to save `teamId: finalTeamId` where `finalTeamId` could be `undefined` for admin users who don't have a specific team assignment. Firebase doesn't allow `undefined` values in documents.

### Solution
Updated `services/activity-service.ts` to:
1. Handle admin users specially by assigning `teamId: "admin"` and `teamName: "Administração"`
2. Only include `teamId` and `teamName` fields in the activity document if they have valid values
3. Use conditional object assignment to avoid undefined values

### Code Change
```typescript
// Handle different user types for teamId assignment
let activityTeamId: string | undefined;

if (finalTeamId) {
  // User has a valid teamId
  activityTeamId = finalTeamId;
  // ... get team name
} else if (userInfo.role === 'admin') {
  // Admin users without teamId get special handling
  activityTeamId = "admin";
  teamName = "Administração";
} else {
  // Regular users without teamId - handle gracefully
  activityTeamId = undefined;
  teamName = undefined;
}

// Prepare activity data, only including teamId if it has a value
const activityData: any = {
  type,
  title,
  description,
  userId,
  userName: userInfo.name,
  userRegistrationNumber: userInfo.registrationNumber,
  timestamp: serverTimestamp(),
  metadata: metadata || {}
};

// Only include teamId and teamName if they have valid values
if (activityTeamId) {
  activityData.teamId = activityTeamId;
}
if (teamName) {
  activityData.teamName = teamName;
}
```

## Expected Result
- Deletion operations should now complete without JavaScript errors
- Activity logging should work correctly for all user types (admin, team leaders, team members)
- All deletion functions should receive the required `userId` parameter
- Team names should be properly resolved and stored in activity logs
- Admin users get special "admin" teamId and "Administração" teamName in activity logs
- Form uploads should work correctly for both admin and team users
- No more undefined field value errors in Firebase
- Better error handling for unauthenticated users and missing team information
- Proper team-based filtering maintained for activity feeds

## Testing
To test the fix:
1. Log in to the application
2. Navigate to any module (Textos, NIPs, Contactos, etc.)
3. Try to delete an item
4. Verify that:
   - No JavaScript errors appear in the console
   - The item is deleted successfully
   - Activity logging works correctly
   - Success/error messages display properly
