# Regras de Segurança e Permissões

## Regra Fundamental de Implementação
- Implementação deve ser progressiva e sempre manter o sistema funcional
- Testar exaustivamente cada nova funcionalidade antes de prosseguir
- Manter backup funcional antes de cada implementação significativa
- Implementar feature flags para controlar novas funcionalidades
- Documentar cada etapa da implementação
- Realizar testes de regressão após cada modificação

## Roles (Funções)
- **Admin**: Acesso total ao sistema
- **Team Leader**: Acesso à gestão da sua equipa
- **Team Member**: Acesso limitado às funcionalidades da sua equipa

## Permissões por Role

### Admin
- Acesso total a todas as páginas
- Criar/editar/excluir utilizadores
- Criar/editar/excluir equipas
- Gerir permissões de acesso
- Ver todos os registos de todas as equipas
- Gerir configurações do sistema
- Acesso à seção "Administração" no sidebar

### Team Leader
- Ver membros da sua equipa
- Ver registos da sua equipa
- Gerir registos da sua equipa
- Acesso às páginas permitidas pelo admin
- Sem acesso à seção administrativa

### Team Member
- Ver registos da sua equipa
- Criar novos registos para sua equipa
- Acesso às páginas permitidas pelo admin
- Sem acesso à seção administrativa

## Estrutura de Equipas
- Cada equipa tem um ou mais líderes
- Equipas podem ter múltiplos membros
- Membros só podem pertencer a uma equipa
- Cada equipa vê apenas seus próprios registos

## Páginas Restritas por Equipa
- Registos
- Condutores
- Identificações

## Páginas Compartilhadas
- Acesso controlado dinamicamente pelo admin
- Visibilidade baseada em permissões

## Regras de Acesso a Dados
- Registos filtrados por teamId
- Validações em múltiplas camadas (cliente/servidor)
- Verificações de permissões em todas as operações

## Seção Administrativa
- Exclusiva para admins
- Interface dedicada para gestão de:
  - Utilizadores
  - Equipas
  - Permissões
  - Configurações do sistema

### Dashboard Administrativo de Auditoria
- Página dedicada para monitoramento de atividades do sistema
- Registro detalhado de todas as operações:
  - Criação/modificação/exclusão de registos
  - Alterações em permissões
  - Modificações em equipas
  - Acessos ao sistema
  - Tentativas de acesso não autorizado
- Filtros avançados por:
  - Data/hora
  - Tipo de operação
  - Usuário
  - Equipa
  - Recurso afetado
- Exportação de logs para análise
- Alertas para atividades suspeitas
- Métricas de utilização do sistema
- Histórico completo de alterações com:
  - Quem fez a alteração
  - O que foi alterado
  - Quando foi alterado
  - Estado anterior e atual
  - Motivo da alteração (quando aplicável)

## Adaptação de Formulários e Componentes Existentes

### Formulários
- Todos os formulários devem incluir validação de equipa
- Submissões devem incluir teamId automaticamente
- Implementar verificações de permissão antes da submissão
- Adaptar:
  - Auto Notícia Form
  - Detenção Form
  - Identificação Form
  - Viatura Form
  - Condutor Form
  - Abastecimento Form
  - Profile Form
  - Estabelecimento Form
  - Finalizar Serviço Form

### Componentes
- Adaptar componentes de listagem para filtrar por equipa
- Modificar componentes de edição para respeitar permissões
- Atualizar componentes de visualização para mostrar apenas dados permitidos
- Implementar verificações de acesso em todos os componentes interativos

### Regras de Submissão
- Validar permissões antes de qualquer operação CRUD
- Incluir metadata da equipa em todas as submissões
- Implementar verificações de integridade dos dados
- Garantir que usuários só podem submeter para suas próprias equipas
- Manter histórico de alterações com informações do autor

### Modificações Necessárias
- Atualizar queries para incluir filtros por equipa
- Adaptar interfaces de usuário para mostrar informações da equipa
- Modificar lógica de autorização em todos os endpoints
- Implementar validações específicas por tipo de formulário
- Garantir que edições respeitem as permissões da equipa

## Notas de Implementação
- Manter design consistente
- Garantir responsividade
- Implementar feedback de ações
- Logging de operações administrativas
- Otimização de performance

