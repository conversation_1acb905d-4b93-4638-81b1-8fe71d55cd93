<svg width="4089" height="3067" viewBox="0 0 4089 3067" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#paint0_angular_748_447_clip_path)" data-figma-skip-parse="true"><g transform="matrix(-2.15631 1.5335 -2.0445 -2.87531 2156.31 1533.5)"><foreignObject x="-1000.53" y="-1000.53" width="2001.05" height="2001.05"><div xmlns="http://www.w3.org/1999/xhtml" style="background:conic-gradient(from 90deg,rgba(7, 15, 87, 1) 0deg,rgba(0, 255, 195, 1) 360deg);height:100%;width:100%;opacity:1"></div></foreignObject></g></g><path d="M0 11.9106C0 5.33255 5.33216 0 11.9097 0H4077.09C4083.67 0 4089 5.33259 4089 11.9107V3055.09C4089 3061.67 4083.67 3067 4077.09 3067H11.9098C5.33224 3067 0 3061.67 0 3055.09V11.9106Z" data-figma-gradient-fill="{&#34;type&#34;:&#34;GRADIENT_ANGULAR&#34;,&#34;stops&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.030887657776474953,&#34;g&#34;:0.062035549432039261,&#34;b&#34;:0.34236654639244080,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:1.0,&#34;b&#34;:0.76666653156280518,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;stopsVar&#34;:[{&#34;color&#34;:{&#34;r&#34;:0.030887657776474953,&#34;g&#34;:0.062035549432039261,&#34;b&#34;:0.34236654639244080,&#34;a&#34;:1.0},&#34;position&#34;:0.0},{&#34;color&#34;:{&#34;r&#34;:0.0,&#34;g&#34;:1.0,&#34;b&#34;:0.76666653156280518,&#34;a&#34;:1.0},&#34;position&#34;:1.0}],&#34;transform&#34;:{&#34;m00&#34;:-4312.616699218750,&#34;m01&#34;:-4089.000488281250,&#34;m02&#34;:6357.11718750,&#34;m10&#34;:3067.0002441406250,&#34;m11&#34;:-5750.6250,&#34;m12&#34;:2875.31250},&#34;opacity&#34;:1.0,&#34;blendMode&#34;:&#34;NORMAL&#34;,&#34;visible&#34;:true}"/>
<g filter="url(#filter0_d_748_447)">
<path d="M2494.51 1204.42H2750.59L2976.3 1653.82L3373.07 1204.42H3621.05L3078.53 1816.78C3043.1 1856.26 2995.53 1892.69 2929.74 1892.69C2866.99 1892.69 2831.56 1859.29 2809.3 1816.78L2494.51 1204.42Z" fill="white"/>
</g>
<g filter="url(#filter1_d_748_447)">
<path d="M2460.37 1422.04C2460.37 1590.06 2313.61 1708.48 2078.78 1708.48H1724.53L1694.16 1881.56H1485.66L1546.39 1535.4H2129.39C2202.27 1535.4 2246.8 1497.95 2246.8 1445.32C2246.8 1405.84 2215.43 1380.54 2156.72 1380.54H1573.72L1743.76 1204.42H2168.87C2357.13 1204.42 2460.37 1292.48 2460.37 1422.04Z" fill="white"/>
</g>
<g filter="url(#filter2_d_748_447)">
<path d="M870.612 1707.47H1447.54L1281.55 1881.56H629.718L750.165 1204.42H1533.58L1366.57 1380.54H928.305L914.135 1461.51H1449.57L1303.82 1615.36H886.807L870.612 1707.47Z" fill="white"/>
</g>
<g filter="url(#filter3_d_748_447)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M2465.25 1186.56H2761.61L2980.4 1622.19L3365.01 1186.56H3660.73L3091.86 1828.67L3091.82 1828.71C3054.94 1869.81 3002.92 1910.56 2929.74 1910.56C2894.97 1910.56 2866.79 1901.23 2844.08 1885.3C2821.58 1869.53 2805.48 1848 2793.47 1825.07L2793.44 1825.01L2465.25 1186.56ZM2976.3 1653.82L2750.59 1204.42H2494.51L2809.29 1816.78C2831.56 1859.29 2866.99 1892.69 2929.74 1892.69C2995.53 1892.69 3043.1 1856.26 3078.53 1816.78L3621.05 1204.42H3373.07L2976.3 1653.82Z" fill="#02C6AA"/>
</g>
<g filter="url(#filter4_d_748_447)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M1546.39 1535.4H2129.39C2202.27 1535.4 2246.8 1497.95 2246.8 1445.32C2246.8 1405.84 2215.43 1380.54 2156.72 1380.54H1573.72L1743.76 1204.42H2168.87C2357.13 1204.42 2460.37 1292.48 2460.37 1422.04C2460.37 1590.06 2313.61 1708.48 2078.78 1708.48H1724.53L1694.16 1881.56H1485.66L1546.39 1535.4ZM1739.53 1726.34H2078.78C2199.02 1726.34 2298.56 1696.04 2368.5 1642.44C2438.81 1588.56 2478.23 1511.78 2478.23 1422.04C2478.23 1352.03 2450.09 1292.3 2396.15 1250.53C2342.72 1209.15 2265.54 1186.56 2168.87 1186.56H1736.18L1531.64 1398.4H2156.72C2183.83 1398.4 2202.1 1404.29 2213.14 1412.47C2223.5 1420.16 2228.94 1430.88 2228.94 1445.32C2228.94 1466.13 2220.34 1483.49 2204.37 1496.06C2188.06 1508.92 2162.93 1517.54 2129.39 1517.54H1531.39L1464.39 1899.42H1709.16L1739.53 1726.34Z" fill="#02C6AA"/>
</g>
<g filter="url(#filter5_d_748_447)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M1303.82 1615.36H886.807L870.613 1707.47H1447.54L1281.55 1881.56H629.719L750.166 1204.42H1533.58L1366.57 1380.54H928.306L914.136 1461.51H1449.57L1303.82 1615.36ZM935.395 1443.65H1491.09L1311.5 1633.22H901.803L891.889 1689.61H1489.26L1289.2 1899.42H608.399L735.201 1186.56H1575.13L1374.25 1398.4H943.313L935.395 1443.65Z" fill="#02C6AA"/>
</g>
<defs>
<clipPath id="paint0_angular_748_447_clip_path"><path d="M0 11.9106C0 5.33255 5.33216 0 11.9097 0H4077.09C4083.67 0 4089 5.33259 4089 11.9107V3055.09C4089 3061.67 4083.67 3067 4077.09 3067H11.9098C5.33224 3067 0 3061.67 0 3055.09V11.9106Z"/></clipPath><filter id="filter0_d_748_447" x="2490.51" y="1204.42" width="1134.54" height="701.269" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="9"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_748_447"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_748_447" result="shape"/>
</filter>
<filter id="filter1_d_748_447" x="1481.66" y="1204.42" width="982.711" height="690.135" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="9"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_748_447"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_748_447" result="shape"/>
</filter>
<filter id="filter2_d_748_447" x="625.718" y="1204.42" width="911.859" height="690.135" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="9"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_748_447"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_748_447" result="shape"/>
</filter>
<filter id="filter3_d_748_447" x="2461.25" y="1186.56" width="1203.49" height="736.992" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="9"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_748_447"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_748_447" result="shape"/>
</filter>
<filter id="filter4_d_748_447" x="1460.39" y="1186.56" width="1021.84" height="725.859" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="9"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_748_447"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_748_447" result="shape"/>
</filter>
<filter id="filter5_d_748_447" x="604.399" y="1186.56" width="974.732" height="725.859" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="9"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_748_447"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_748_447" result="shape"/>
</filter>
</defs>
</svg>
