"use client";

import React, { createContext, useContext, useEffect, useState, useCallback } from "react";
import {
  User,
  signInWithEmailAndPassword,
  signOut as firebaseSignOut,
  onAuthStateChanged,
} from "firebase/auth";
import { auth, db } from "@/lib/firebase";
import { doc, getDoc, setDoc, serverTimestamp } from "firebase/firestore";
import { useRouter } from "next/navigation";
import { showSuccessToast, showErrorToast } from "@/lib/toast-utils";
import { UserRole } from "@/types/team";
import { getUserTeam } from "@/services/teams-service";
import { PagePermission, shouldHidePage } from "@/types/permissions";
import { getAllUserPermissions } from "@/services/permissions-service";

interface UserProfile {
  fullName?: string;
  registrationNumber?: string;
  category?: string;
  profileCompleted?: boolean;
  role?: UserRole;
  teamId?: string;
  canEditProfile?: boolean; // Indica se o utilizador pode editar seu próprio perfil
}

interface AuthContextType {
  user: User | null;
  userProfile: UserProfile | null;
  loading: boolean;
  profileLoading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signOut: () => Promise<void>;
  updateUserProfile: (profile: UserProfile) => Promise<void>;
  isAdmin: boolean;
  isTeamLeader: boolean;
  isTeamMember: boolean;
  userTeamId: string | undefined;
  canEditProfile: boolean; // Indica se o utilizador pode editar seu próprio perfil
  pagePermissions: PagePermission[]; // Permissões de página do usuário
  permissionsLoading: boolean; // Indica se as permissões estão sendo carregadas
  isPageHidden: (pageId: string) => boolean; // Função para verificar se uma página deve ser ocultada
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [profileLoading, setProfileLoading] = useState(true);
  const [userTeamId, setUserTeamId] = useState<string | undefined>(undefined);
  const [pagePermissions, setPagePermissions] = useState<PagePermission[]>([]);
  const [permissionsLoading, setPermissionsLoading] = useState(true);
  const router = useRouter();

  // Computed properties for user roles
  const isAdmin = userProfile?.role === UserRole.ADMIN;
  const isTeamLeader = userProfile?.role === UserRole.TEAM_LEADER;
  const isTeamMember = userProfile?.role === UserRole.TEAM_MEMBER;

  // Função para verificar se uma página deve ser ocultada
  const isPageHidden = useCallback((pageId: string): boolean => {
    // Administradores sempre veem todas as páginas
    if (isAdmin) return false;

    // Verificar nas permissões do usuário
    return shouldHidePage(pageId, pagePermissions);
  }, [isAdmin, pagePermissions]);

  // Verificar se o utilizador pode editar seu próprio perfil
  // Por padrão, se não estiver definido, o utilizador pode editar seu perfil
  const canEditProfile = userProfile?.canEditProfile !== false;

  // Função para buscar as permissões do usuário
  const fetchUserPermissions = useCallback(async (userId: string, teamId?: string) => {
    setPermissionsLoading(true);
    try {
      console.log("Buscando permissões do usuário:", userId, "Equipe:", teamId);

      // Obter todas as permissões do usuário (incluindo as da equipe)
      const permissions = await getAllUserPermissions(userId, teamId);
      console.log("Permissões encontradas:", permissions.length);

      setPagePermissions(permissions);
    } catch (error) {
      console.error("Erro ao buscar permissões do usuário:", error);
      setPagePermissions([]);
    } finally {
      setPermissionsLoading(false);
    }
  }, []);

  // Função para buscar o perfil do usuário
  const fetchUserProfile = useCallback(async (userId: string) => {
    console.log("Fetching user profile for:", userId);
    setProfileLoading(true);

    try {
      // Buscar o perfil do usuário no Firestore
      const userDocRef = doc(db, "users", userId);
      const userDoc = await getDoc(userDocRef);

      if (userDoc.exists()) {
        const userData = userDoc.data() as UserProfile;
        console.log("User profile found:", userData);
        setUserProfile(userData);

        // Se o usuário tiver um papel definido, buscar informações da equipa
        if (userData.role === UserRole.TEAM_LEADER || userData.role === UserRole.TEAM_MEMBER) {
          try {
            // Se o usuário já tem teamId definido, usar esse valor diretamente
            if (userData.teamId) {
              console.log("Usando teamId do perfil do usuário:", userData.teamId);
              setUserTeamId(userData.teamId);

              // Buscar permissões do usuário
              await fetchUserPermissions(userId, userData.teamId);
            } else {
              // Caso contrário, buscar a equipa do usuário
              console.log("Buscando equipa do usuário...");
              try {
                const team = await getUserTeam(userId);
                if (team) {
                  console.log("User team found:", team.name);
                  setUserTeamId(team.id);

                  // Atualizar o perfil do usuário com o teamId
                  await setDoc(doc(db, "users", userId), {
                    teamId: team.id,
                    updatedAt: serverTimestamp(),
                  }, { merge: true });

                  // Buscar permissões do usuário
                  await fetchUserPermissions(userId, team.id);
                } else {
                  console.warn("Usuário é líder ou membro, mas não tem equipa associada");
                  // Buscar apenas permissões do usuário
                  await fetchUserPermissions(userId);
                }
              } catch (error) {
                console.error("Erro ao obter equipa do usuário:", error);
                // Buscar apenas permissões do usuário
                await fetchUserPermissions(userId);
              }
            }
          } catch (error) {
            console.error("Erro ao buscar equipa do usuário:", error);
            // Buscar apenas permissões do usuário
            await fetchUserPermissions(userId);
          }
        } else if (userData.role === UserRole.ADMIN) {
          // Administradores não precisam de permissões específicas
          setPagePermissions([]);
          setPermissionsLoading(false);
        } else {
          // Usuário sem papel definido, buscar apenas permissões do usuário
          await fetchUserPermissions(userId);
        }
      } else {
        console.log("User profile not found, creating empty profile");
        // Se o documento não existir, definimos um perfil vazio
        setUserProfile({ profileCompleted: false });
        setPagePermissions([]);
        setPermissionsLoading(false);
      }
    } catch (error) {
      console.error("Erro ao buscar perfil do usuário:", error);
      setUserProfile({ profileCompleted: false });
      setPagePermissions([]);
      setPermissionsLoading(false);
    } finally {
      setProfileLoading(false);
    }
  }, [setProfileLoading, setUserProfile, setUserTeamId, fetchUserPermissions]);

  // Efeito para configurar a autenticação
  useEffect(() => {
    let unsubscribeAuth: (() => void) | null = null;
    let isInitialized = false;

    console.log("Inicializando contexto de autenticação...");
    setLoading(true);

    // Verificar se há dados de usuário em localStorage para estado inicial
    if (typeof window !== 'undefined') {
      const cachedUser = localStorage.getItem('authUser');
      if (cachedUser) {
        try {
          console.log("Usando dados de usuário em cache temporariamente");
          JSON.parse(cachedUser);
        } catch (e) {
          console.error('Erro ao parsear dados de usuário em cache:', e);
          localStorage.removeItem('authUser');
        }
      }
    }

    // Função para lidar com mudanças no estado de autenticação
    const handleAuthStateChange = async (authUser: User | null) => {
      console.log("Estado de autenticação alterado:", authUser ? "Autenticado" : "Não autenticado");

      if (authUser) {
        // Usuário autenticado
        console.log("Usuário autenticado:", authUser.uid);

        // Armazenar dados básicos do usuário em localStorage
        const userData = {
          uid: authUser.uid,
          email: authUser.email,
          displayName: authUser.displayName,
          photoURL: authUser.photoURL,
        };

        // Armazenar no localStorage (cliente)
        if (typeof window !== 'undefined') {
          localStorage.setItem('authUser', JSON.stringify(userData));
        }

        // Atualizar estado do usuário
        setUser(authUser);

        // Buscar perfil do usuário
        try {
          await fetchUserProfile(authUser.uid);
        } catch (error) {
          console.error("Erro ao buscar perfil do usuário:", error);
          setProfileLoading(false);
        }
      } else {
        // Usuário não autenticado
        console.log("Usuário não autenticado");

        // Limpar dados do localStorage
        if (typeof window !== 'undefined') {
          localStorage.removeItem('authUser');
          sessionStorage.removeItem('lastCheckedPath');
        }

        // Limpar estados
        setUser(null);
        setUserProfile(null);
        setProfileLoading(false);
      }

      // Finalizar carregamento
      if (isInitialized) {
        setLoading(false);
      } else {
        isInitialized = true;
        // Pequeno atraso para garantir que o estado seja atualizado corretamente
        setTimeout(() => {
          setLoading(false);
        }, 500);
      }
    };

    // Inscrever-se para mudanças no estado de autenticação
    unsubscribeAuth = onAuthStateChanged(auth, handleAuthStateChange);

    // Limpar inscrições quando o componente for desmontado
    return () => {
      if (unsubscribeAuth) {
        console.log("Limpando inscrição de autenticação");
        unsubscribeAuth();
      }
    };
  }, [fetchUserProfile]);

  // Função para atualizar o perfil do usuário
  const updateUserProfile = async (profile: UserProfile) => {
    if (!user) {
      throw new Error("Usuário não autenticado");
    }

    try {
      console.log("Updating user profile:", profile);

      // Salvar os dados no Firestore
      await setDoc(doc(db, "users", user.uid), {
        ...profile,
        updatedAt: serverTimestamp(),
      }, { merge: true });

      // Atualiza o estado local com uma cópia profunda para garantir que as mudanças sejam detectadas
      setUserProfile(prevProfile => {
        const updatedProfile = {
          ...(prevProfile || {}),
          ...profile
        };
        console.log("Estado do perfil atualizado:", updatedProfile);
        return updatedProfile;
      });

      console.log("Profile updated successfully");
      return;
    } catch (error) {
      console.error("Erro ao atualizar perfil:", error);
      throw error;
    }
  };

  const signIn = async (email: string, password: string) => {

    try {
      setLoading(true);
      console.log("Iniciando processo de login com Firebase...");

      // Autenticar com Firebase (agora temos certeza que auth não é null)
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      console.log("Login Firebase bem-sucedido para:", user.email);

      // Mostrar notificação de sucesso
      showSuccessToast("Sessão iniciada com sucesso!", {
        description: "Bem-vindo de volta!",
        icon: "👋",
      });

      // Verificar se o perfil está completo para decidir o redirecionamento
      // (agora temos certeza que db não é null)
      const profile = await getDoc(doc(db, "users", user.uid));
      const profileData = profile.exists() ? profile.data() as UserProfile : null;

      console.log("Profile data:", profileData);

      // Aguardar um intervalo maior para garantir que o estado de autenticação foi processado
      // Isso ajuda a evitar problemas de corrida com o RouteGuard
      await new Promise(resolve => setTimeout(resolve, 800));

      // Limpar o caminho verificado anteriormente para permitir redirecionamento
      sessionStorage.removeItem('lastCheckedPath');

      // Redirecionamento baseado no estado do perfil
      if (!profileData || !profileData.profileCompleted) {
        console.log("Redirecting to complete profile");
        router.push("/complete-profile");
      } else {
        console.log("Redirecting to dashboard");
        router.push("/dashboard");
      }

      // Retornar sem esperar pelo redirecionamento
      return;
    } catch (error: unknown) {
      // Tratar erros de autenticação
      let errorMessage = "Ocorreu um erro ao iniciar sessão.";
      let errorDescription = "Verifique a sua ligação à internet e tente novamente.";

      if (error && typeof error === 'object' && 'code' in error) {
        const firebaseError = error as { code: string };

        if (firebaseError.code === "auth/invalid-credential") {
          errorMessage = "Email ou palavra-passe incorretos.";
          errorDescription = "Verifique os seus dados e tente novamente.";
        } else if (firebaseError.code === "auth/user-not-found") {
          errorMessage = "Utilizador não encontrado.";
          errorDescription = "Este email não está registado no sistema.";
        } else if (firebaseError.code === "auth/wrong-password") {
          errorMessage = "Palavra-passe incorreta.";
          errorDescription = "Verifique a sua palavra-passe e tente novamente.";
        } else if (firebaseError.code === "auth/too-many-requests") {
          errorMessage = "Demasiadas tentativas.";
          errorDescription = "A sua conta foi temporariamente bloqueada. Tente novamente mais tarde.";
        }
      }

      showErrorToast(errorMessage, {
        description: errorDescription,
      });
      throw error;
    } finally {
      // Definir loading como false após um pequeno atraso
      // Isso evita que o estado de loading seja alterado muito rapidamente
      setTimeout(() => {
        setLoading(false);
      }, 300);
    }
  };

  const signOut = async () => {
    // Definir uma flag para controlar se o componente ainda está montado
    let isMounted = true;

    // Definir um timeout para redirecionar, mesmo se houver erros
    const redirectTimeout = setTimeout(() => {
      if (typeof window !== 'undefined') {
        window.location.href = '/login';
      }
    }, 1000); // Redirecionar após 1 segundo, mesmo se houver erros

    try {
      // Marcar que estamos no processo de logout para os scripts de gerenciamento de cache
      if (typeof window !== 'undefined' && window.sessionStorage) {
        sessionStorage.setItem('loggingOut', 'true');
      }

      // Limpar a UI antes de fazer logout
      if (typeof window !== 'undefined') {
        // Desativar transições para evitar problemas visuais durante o logout
        document.documentElement.classList.add('no-transitions');

        // Limpar quaisquer estados de UI que possam interferir com a página de login
        document.body.classList.remove('overflow-hidden');

        // Definir uma variável global para indicar que estamos fazendo logout
        // Isso será usado pelos componentes para evitar manipulações do DOM durante o logout
        (window as any).__LOGGING_OUT = true;
      }

      // Limpar dados do localStorage e cookies antes de fazer logout
      // Isso evita problemas de acesso a dados após o logout
      if (typeof window !== 'undefined') {
        localStorage.removeItem('authUser');
        sessionStorage.removeItem('lastCheckedPath');
        sessionStorage.removeItem('authSessionId');

        // Limpar caches específicos relacionados à autenticação
        if ('caches' in window) {
          try {
            const cacheNames = await caches.keys();
            const authCaches = cacheNames.filter(name =>
              name.includes('auth') ||
              name.includes('user') ||
              name.includes('firebase')
            );

            await Promise.all(
              authCaches.map(cacheName => {
                console.log('Limpando cache de autenticação:', cacheName);
                return caches.delete(cacheName);
              })
            );
            console.log('Caches de autenticação foram limpos');
          } catch (cacheError) {
            console.error('Erro ao limpar caches de autenticação:', cacheError);
          }
        }
      }

      document.cookie = 'authUser=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
      console.log("Dados do usuário removidos do localStorage e cookies");

      // Fazer logout do Firebase
      await firebaseSignOut(auth);

      // Verificar se o componente ainda está montado antes de mostrar o toast
      if (isMounted) {
        showSuccessToast("Sessão terminada com sucesso!", {
          description: "Até breve!",
          icon: "👋",
        });
      }

      // Usar window.location para uma navegação completa em vez de router.push
      // Isso garante que a página seja completamente recarregada
      if (typeof window !== 'undefined') {
        // Limpar o timeout, pois vamos redirecionar agora
        clearTimeout(redirectTimeout);
        // Pequeno atraso para garantir que tudo seja limpo antes de redirecionar
        setTimeout(() => {
          window.location.href = '/login';
        }, 100);
      } else {
        router.push("/login");
      }
    } catch (error) {
      console.error("Erro ao fazer logout:", error);

      // Mesmo em caso de erro, mostrar uma mensagem e redirecionar
      if (isMounted) {
        showErrorToast("Ocorreu um erro ao terminar sessão.", {
          description: "Você será redirecionado para a página de login.",
        });
      }

      // Não precisamos limpar o timeout aqui, pois queremos que o redirecionamento ocorra
    } finally {
      // Marcar o componente como desmontado
      isMounted = false;

      // Restaurar transições após um pequeno atraso
      if (typeof window !== 'undefined') {
        setTimeout(() => {
          document.documentElement.classList.remove('no-transitions');

          // Limpar a flag de logout após o redirecionamento
          if (window.sessionStorage) {
            sessionStorage.removeItem('loggingOut');
          }
        }, 500);
      }
    }
  };

  return (
    <AuthContext.Provider value={{
      user,
      userProfile,
      loading,
      profileLoading,
      signIn,
      signOut,
      updateUserProfile,
      isAdmin,
      isTeamLeader,
      isTeamMember,
      userTeamId,
      canEditProfile,
      pagePermissions,
      permissionsLoading,
      isPageHidden
    }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth deve ser usado dentro de um AuthProvider");
  }
  return context;
}
