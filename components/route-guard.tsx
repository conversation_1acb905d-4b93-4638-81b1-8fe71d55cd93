"use client";

import { useAuth } from "@/contexts/auth-context";
import { useRouter, usePathname } from "next/navigation";
import { useEffect, useState, useRef } from "react";
import { UserRole } from "@/types/team";
import { hasPermission } from "@/lib/permissions";
import { toast } from "sonner";
import { SYSTEM_PAGES } from "@/types/permissions";

interface RouteGuardProps {
  children: React.ReactNode;
}

export function RouteGuard({ children }: RouteGuardProps) {
  const { user, loading, profileLoading, userProfile, isAdmin, isPageHidden, permissionsLoading } = useAuth();
  const router = useRouter();
  const pathname = usePathname();
  const [isRedirecting, setIsRedirecting] = useState(false);
  const redirectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastPathRef = useRef<string>(pathname);

  // Rotas públicas que não precisam de autenticação
  const publicRoutes = ['/login', '/register', '/forgot-password', '/api/auth'];
  const isPublicRoute = publicRoutes.some(route => pathname.startsWith(route));

  // Verificar se é a rota de completar perfil
  const isCompleteProfileRoute = pathname.startsWith('/complete-profile');

  // Rotas administrativas que requerem papel de admin
  const adminRoutes = ['/admin'];
  const isAdminRoute = adminRoutes.some(route => pathname.startsWith(route));

  // Rotas restritas por equipa (apenas membros da equipa podem acessar)
  const teamRestrictedRoutes = ['/registos', '/condutores', '/identificacoes', '/relatorios'];
  const isTeamRestrictedRoute = teamRestrictedRoutes.some(route => pathname.startsWith(route));

  // Rotas compartilhadas (todos os usuários autenticados podem acessar)
  const sharedRoutes = [
    '/dashboard',
    '/formularios',
    '/textos',
    '/nips',
    '/contactos',
    '/estabelecimentos',
    '/viaturas',
    '/account'
  ];
  const isSharedRoute = sharedRoutes.some(route => pathname.startsWith(route));

  // Verificar se o usuário tem permissão para acessar a rota atual
  const hasRoutePermission = () => {
    // Rotas públicas não precisam de verificação de permissão
    if (isPublicRoute) return true;

    // A rota de completar perfil é acessível para qualquer usuário autenticado
    if (isCompleteProfileRoute) return true;

    // Rotas administrativas requerem papel de admin
    if (isAdminRoute) return isAdmin;

    // Verificar se a página está oculta para o usuário
    // Primeiro, encontrar a página correspondente à rota atual
    const currentPage = SYSTEM_PAGES.find(page => pathname.startsWith(page.url));
    if (currentPage && isPageHidden(currentPage.id)) {
      return false;
    }

    // Rotas restritas por equipa requerem pelo menos papel de membro de equipa
    if (isTeamRestrictedRoute) {
      return userProfile?.role === UserRole.ADMIN ||
             userProfile?.role === UserRole.TEAM_LEADER ||
             userProfile?.role === UserRole.TEAM_MEMBER;
    }

    // Rotas compartilhadas podem ser acessadas por qualquer usuário autenticado
    if (isSharedRoute) return true;

    // Para outras rotas, verificar se o usuário tem algum papel definido
    return userProfile?.role !== undefined;
  };

  // Limpar timeout quando o componente for desmontado
  useEffect(() => {
    return () => {
      if (redirectTimeoutRef.current) {
        clearTimeout(redirectTimeoutRef.current);
      }
    };
  }, []);

  // Detectar mudanças de rota
  useEffect(() => {
    if (pathname !== lastPathRef.current) {
      console.log(`RouteGuard: Rota alterada de ${lastPathRef.current} para ${pathname}`);
      lastPathRef.current = pathname;
      setIsRedirecting(false);

      if (redirectTimeoutRef.current) {
        clearTimeout(redirectTimeoutRef.current);
        redirectTimeoutRef.current = null;
      }
    }
  }, [pathname]);

  // Efeito para verificar autenticação e permissões, redirecionando quando necessário
  useEffect(() => {
    // Evitar redirecionamentos em cascata ou durante carregamento
    if (isRedirecting || loading || profileLoading || permissionsLoading) return;

    // Verificar autenticação e permissões apenas após o carregamento inicial
    if (!loading && !profileLoading && !permissionsLoading) {
      // Caso 1: Usuário não autenticado tentando acessar rota protegida
      if (!user && !isPublicRoute) {
        console.log("RouteGuard: Usuário não autenticado, redirecionando para login");
        setIsRedirecting(true);

        // Usar um timeout para evitar redirecionamentos muito rápidos
        redirectTimeoutRef.current = setTimeout(() => {
          router.push("/login");
        }, 500);
      }
      // Caso 2: Usuário autenticado tentando acessar rota pública
      else if (user && isPublicRoute && pathname !== '/forgot-password') {
        console.log("RouteGuard: Usuário já autenticado, redirecionando para dashboard");
        setIsRedirecting(true);

        // Usar um timeout para evitar redirecionamentos muito rápidos
        redirectTimeoutRef.current = setTimeout(() => {
          router.push("/dashboard");
        }, 500);
      }
      // Caso 3: Usuário autenticado sem permissão para a rota atual
      else if (user && !isPublicRoute && !hasRoutePermission()) {
        console.log("RouteGuard: Usuário sem permissão para acessar esta rota");
        setIsRedirecting(true);

        toast.error("Acesso negado", {
          description: "Você não tem permissão para acessar esta página.",
        });

        // Redirecionar para o dashboard
        redirectTimeoutRef.current = setTimeout(() => {
          router.push("/dashboard");
        }, 500);
      }
    }
  }, [user, loading, profileLoading, permissionsLoading, isPublicRoute, pathname, router, isRedirecting, userProfile, isAdmin, isPageHidden]);

  // Mostrar um indicador de carregamento enquanto verifica a autenticação
  if (loading || profileLoading || permissionsLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Carregando...</p>
        </div>
      </div>
    );
  }

  // Renderizar o conteúdo apenas se o usuário tiver permissão
  if (isPublicRoute || (user && hasRoutePermission())) {
    return <>{children}</>;
  }

  // Caso contrário, mostrar uma tela de carregamento enquanto redireciona
  return (
    <div className="flex min-h-screen items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mx-auto"></div>
        <p className="mt-4 text-muted-foreground">Redirecionando...</p>
      </div>
    </div>
  );
}
